<template>
  <div class="wrap">
    <div class="left" @click="leftJump">
      <img :src="pagcor1" class="img1" alt="Pagcor标识1" loading="lazy" />
      <img :src="pagcor2" class="img2" alt="Pagcor标识2" loading="lazy" />
    </div>
    <!-- 新增竖线元素 -->
    <div class="vertical-line"></div>
    <div class="right">
      <img :src="pagcor4" class="img3" alt="Pagcor标识3" loading="lazy" />
    </div>
  </div>
</template>

<script lang="ts" setup>
defineOptions({
  name: "ProviderLogo",
});

import pagcor1 from "@/assets/icons/login/pagcor1.png";
import pagcor2 from "@/assets/icons/login/pagcor2.png";
import pagcor4 from "@/assets/icons/login/pagcor4.png";

const leftJump = () => {
  window.open("https://www.pagcor.ph/regulatory/responsible-gaming.php", "_blank");
};
</script>

<style scoped lang="scss">
.wrap {
  display: flex;
  align-items: center;
  justify-content: center; // 整体水平居中
  width: 100%; // 组件宽度自适应容器
  box-sizing: border-box;
  gap: 16px; // 增加间距

  .left {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0; // 左侧内容不压缩
  }

  .right {
    display: flex;
    align-items: center;
    flex-shrink: 0; // 右侧内容不压缩
  }

  // 竖线样式
  .vertical-line {
    min-width: 1px; // 确保最小宽度
    width: max(1px, 0.267vw); // 使用 max() 确保至少 1px
    height: 20px;
    background-color: #f0f0f0;
    margin: 0 8px; // 竖线两侧额外间距
    flex-shrink: 0; // 防止被压缩
    align-self: center; // 竖线垂直居中
  }

  .img1 {
    width: 30px;
    height: 30px;
    object-fit: contain;
    flex-shrink: 0; // 固定尺寸
  }

  .img2 {
    width: 90px;
    height: 20px;
    object-fit: contain;
    flex-shrink: 0; // 固定尺寸
    cursor: pointer;
  }

  .img3 {
    width: 100%; // 自适应容器宽度
    max-width: 97px; // 最大宽度限制
    height: 29px;
    object-fit: contain;
    min-width: 60px; // 最小宽度保证可读性
  }
}
</style>
