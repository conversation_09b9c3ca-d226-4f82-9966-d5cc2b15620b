/**
 * Store 相关类型定义
 * 统一管理所有 store 的 TypeScript 类型
 */

import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";

// ==================== Global Store Types ====================

/**
 * 用户信息接口
 */
export interface UserInfo {
  id?: number;
  username?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  nickname?: string;
  is_vip?: number;
  [key: string]: any;
}

/**
 * 支付账户接口
 */
export interface PayAccount {
  id?: number;
  account_name?: string;
  account_number?: string;
  bank_name?: string;
  [key: string]: any;
}

/**
 * 注册奖励接口
 */
export interface RegisterAward {
  id?: number;
  amount?: number;
  status?: number;
  [key: string]: any;
}

/**
 * 未读标记接口
 */
export interface UnreadMarks {
  customer_service?: boolean;
  system_message?: boolean;
  [key: string]: any;
}

/**
 * 登录信息接口
 */
export interface LoginInfo {
  token?: string;
  user_info?: UserInfo;
  pay_account?: PayAccount;
  register_award?: RegisterAward;
  unread_marks?: UnreadMarks;
  [key: string]: any;
}

/**
 * 全局 Store 状态接口
 */
export interface GlobalStoreState {
  token: string;
  channel: CHANEL_TYPE;
  userInfo: UserInfo;
  payAccount: PayAccount;
  registerAward: RegisterAward;
  unreadMarks: UnreadMarks;
  balance: number;
  hasUnreadCustomerMsg: boolean;
  hasUnreadIndexboxMsg: boolean;
  hasUnreadNews: boolean;
  showForceUpdateDialog: boolean;
  loginConfig: {
    login_facebook: number;
    login_google: number;
    login_password: number;
  };
}

// ==================== Game Store Types ====================

/**
 * 游戏接口
 */
export interface Game {
  game_id: number;
  id: number;
  is_like: string;
  home_page_label?: string;
  [key: string]: any;
}

/**
 * 游戏分类接口
 */
export interface Category {
  id: number;
  name: string;
  game_type: string;
  sort: number;
  games?: Game[];
  [key: string]: any;
}

/**
 * 游戏厂商接口
 */
export interface Provider {
  id: number | string;
  name: string;
  status: number;
  sort: number;
  icon_home?: string;
  imageUrl?: string;
  imageError?: boolean;
  short_name?: string;
  provider?: string;
  [key: string]: any;
}

/**
 * 游戏配置集合接口
 */
export interface GameAllSet {
  third_company: Provider[];
  game_type: Category[];
  [key: string]: any;
}

/**
 * 直播游戏接口
 */
export interface LiveGame {
  id: string;
  url: string;
  duration: number;
  game_id: number;
  game_name: string;
  [key: string]: any;
}

/**
 * 游戏 Store 状态接口
 */
export interface GameStoreState {
  /** 游戏分类列表 */
  gameTypes: Category[];
  /** 游戏列表 */
  gameList: Game[];
  /** 维护中的游戏ID列表 */
  maintenanceList: number[];
  /** 隐藏的游戏ID列表 */
  hideList: number[];
  /** 可选择的厂商列表（包含本地图片） */
  chooseProviders: Provider[];
  /** 厂商本地图片列表 */
  providerImgs: any[];
  /** 直播游戏列表 */
  liveGame: LiveGame[];
  /** 有效的第三方厂商列表 */
  vaildThirdCompany: Provider[];
  /** 所有第三方厂商列表 */
  allThirdCompany: Provider[];
  /** 配置数据 */
  configData: Record<string, any>;
  iosPopUpWindow: string | number;
}

// ==================== KYC Store Types ====================

/**
 * KYC 详版表单数据接口
 */
export interface KycDetailFormData {
  [key: string]: any;
}

/**
 * KYC 简版表单数据接口
 */
export interface KycSimpleFormData {
  [key: string]: any;
}

/**
 * KYC Store 状态接口
 */
export interface KycStoreState {
  detailFormData: KycDetailFormData;
  simpleFormData: KycSimpleFormData;
  detailDayInputErrTip: string;
  simpleDayInputErrTip: string;
  isSameCurrentAddress: boolean;
  isGovemmentOfficial: boolean;
  curStep: number;
  detailPhotoBase64: string;
  simplePhotoBase64: string;
  showPhoneChangeDialog: boolean;
}

// ==================== 通用类型 ====================

/**
 * API 响应基础接口
 */
export interface ApiResponse<T = any> {
  code?: number;
  message?: string;
  data?: T;
  [key: string]: any;
}

/**
 * 分页数据接口
 */
export interface PaginationData<T = any> {
  list: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore?: boolean;
}

/**
 * 选项接口
 */
export interface Option {
  label: string;
  value: string | number;
  disabled?: boolean;
  [key: string]: any;
}

/**
 * 表单验证规则接口
 */
export interface ValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  validator?: (value: any) => boolean | string;
  [key: string]: any;
}

// ==================== Banner Store Types ====================

/**
 * Banner 数据接口
 */
export interface BannerItem {
  id?: number;
  title?: string;
  duration?: number; // 持续时间（秒）
  sort?: number; // 排序
  home_page_banner?: string; // 首页轮播图
  promo_page_banner?: string; // 促销页轮播图
  rewards_wallet_banner?: string; // 奖金钱包轮播图
  jump_type?: string | number; // 跳转类型
  url?: string; // 跳转链接
  activity_list?: string | number; // 活动列表
  home_page_jump?: number; // 首页跳转
  [key: string]: any;
}

/**
 * Banner Store 状态接口
 */
export interface BannerStoreState {
  banners: BannerItem[]; // 轮播图列表
  isLoading: boolean; // 加载状态
  lastFetchTime: number; // 最后获取时间
  cacheExpiry: number; // 缓存过期时间（毫秒）
}
