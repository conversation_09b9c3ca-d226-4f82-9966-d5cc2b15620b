<!-- filepath: /src/components/ZPopDialog/EnvelopeTip.vue -->
<template>
  <ZPopOverlay :show="showEnvelopeTip">
    <div class="content" :style="{ backgroundImage: `url(${envelope2Image})` }">
      <div class="bonus">{{ envelopeInfo.first_deposit_default_bonus_rate }} Bonus</div>
      <div class="tip">
        {{ amountFormatThousands(envelopeInfo.today_user_recharge_count, 0) }} Players Claimed Today
      </div>

      <div class="btn">
        <GradientButton
          background-gradient="linear-gradient(180deg, #FF5E43 20.59%, #FF916C 94.85%)"
          border-gradient="#FFDFBF"
          :showLight="true"
          @click="handleConfirm"
          >Deposit To Claim</GradientButton
        >
      </div>
      <div class="close" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="36" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore, POP_FORM } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { useGlobalStore } from "@/stores/global";
import { useDepositStore } from "@/stores/deposit";
import { amountFormatThousands } from "@/utils/core/tools";
import { envelopeDisplayManager } from "@/utils/managers/EnvelopeDisplayManager";
// 导入图片资源，确保与预加载使用相同的路径
import envelope2Image from "@/assets/images/popDialog/envelope2.png";

const depositStore = useDepositStore();
const autoPopMgrStore = useAutoPopMgrStore();
const globalStore = useGlobalStore();
const { showEnvelopeTip, envelopeInfo, sourceEnvelope } = storeToRefs(autoPopMgrStore);

/**
 * 记录弹窗显示次数
 */
const handleCount = () => {
  const userId = globalStore.userInfo?.user_id;
  if (!userId) return;
  if (sourceEnvelope.value === POP_FORM.AUTO) {
    envelopeDisplayManager.recordShow(userId);
  }
};

const handleConfirm = () => {
  showEnvelopeTip.value = false;
  depositStore.openDialog(envelopeInfo.value.first_deposit_default_bonus);
};

const handleClose = () => {
  showEnvelopeTip.value = false;
  if (sourceEnvelope.value === POP_FORM.AUTO) {
    AutoPopMgr.destroyCurrentPopup();
  }
};

// 当 showEnvelopeTip 变为 true 时记录弹出次数
watch(
  () => autoPopMgrStore.showEnvelopeTip,
  (newVal) => {
    if (newVal) {
      handleCount();
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  width: 375px;
  height: 667px;
  padding: 20px;
  border-radius: 33px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  z-index: 9;

  .bonus {
    width: 100%;
    position: absolute;
    top: 250px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    text-align: center;
    font-family: D-DIN;
    font-size: 40px;
    font-weight: 700;
    letter-spacing: -1.68px;
  }

  .tip {
    width: 100%;
    position: absolute;
    top: 360px;
    left: 50%;
    transform: translateX(-50%);
    color: #882b00;
    text-align: center;
    font-family: Inter;
    font-size: 16px;
    font-weight: 400;
  }

  .btn {
    width: 228px;
    height: 56px;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: 100% 100%;
    position: absolute;
    top: 400px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }

  .close {
    position: absolute;
    top: 500px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
