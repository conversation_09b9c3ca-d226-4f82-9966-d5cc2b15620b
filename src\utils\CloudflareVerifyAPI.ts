/**
 * Cloudflare 验证 API
 */

import { createApp, h, ref } from "vue";
import { CF_TURNSTILE_TYPE, type TurnstileResult } from "./CloudflareMgr";
import CloudflareVerifyDialog from "@/components/CloudflareVerifyDialog/index.vue";

// 类型定义和接口
export interface VerifyOptions {
  cfType: CF_TURNSTILE_TYPE;
  title?: string;
  description?: string;
  showCancelButton?: boolean;
  siteKey?: string;
  theme?: "light" | "dark" | "auto";
  size?: "normal" | "compact" | "invisible";
  appearance?: "always" | "execute" | "interaction-only";
  autoCloseDelay?: number;
}

export interface VerifyResult {
  success: boolean;
  "cf-token"?: string;
  "cf-scene": string;
  error?: string;
  errorCode?: string;
  retryable?: boolean;
  cancelled?: boolean;
  token?: string;
  cfType?: string;
}

// 核心验证函数 有感验证-弹窗
export function showCloudflareVerify(options: VerifyOptions): Promise<VerifyResult> {
  return new Promise((resolve) => {
    // 创建容器元素
    const container = document.createElement("div");
    document.body.appendChild(container);

    // 创建响应式状态
    const visible = ref(true);

    // 清理函数
    const cleanup = () => {
      setTimeout(() => {
        if (container && container.parentNode) {
          app.unmount();
          container.parentNode.removeChild(container);
        }
      }, 300); // 延迟清理，等待动画完成
    };

    // 处理验证成功
    const handleSuccess = (result: TurnstileResult) => {
      visible.value = false;
      cleanup();
      resolve({
        success: true,
        "cf-token": result["cf-token"],
        "cf-scene": result["cf-scene"],
        // 保持向后兼容
        token: result["cf-token"],
        cfType: result["cf-scene"],
      });
    };

    // 处理验证失败
    const handleError = (error: string) => {
      visible.value = false;
      cleanup();

      // 如果是 TurnstileResult 对象，直接使用其信息
      if (typeof error === "object" && error !== null) {
        const result = error as any;
        resolve({
          success: false,
          "cf-scene": result["cf-scene"] || options.cfType,
          error: result.error,
          errorCode: result.errorCode,
          retryable: result.retryable,
          // 保持向后兼容
          cfType: result["cf-scene"] || options.cfType,
        });
      } else {
        // 字符串错误，使用默认处理
        resolve({
          success: false,
          "cf-scene": options.cfType,
          error: typeof error === "string" ? error : "Verification failed",
          // 保持向后兼容
          cfType: options.cfType,
        });
      }
    };

    // 处理取消
    const handleCancel = () => {
      visible.value = false;
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        cancelled: true,
        // 保持向后兼容
        cfType: options.cfType,
      });
    };

    // 处理弹窗关闭
    const handleUpdateModelValue = (value: boolean) => {
      visible.value = value;
      if (!value) {
        cleanup();
        resolve({
          success: false,
          "cf-scene": options.cfType,
          cancelled: true,
          // 保持向后兼容
          cfType: options.cfType,
        });
      }
    };

    // 创建 Vue 应用实例
    const app = createApp({
      render() {
        // 构建组件属性，只传递有值的属性
        const props: any = {
          modelValue: visible.value,
          "onUpdate:modelValue": handleUpdateModelValue,
          cfType: options.cfType,
          onSuccess: handleSuccess,
          onError: handleError,
          onCancel: handleCancel,
        };

        // 只有当属性有值时才添加到 props 中
        if (options.showCancelButton !== undefined)
          props.showCancelButton = options.showCancelButton;
        if (options.siteKey !== undefined) props.siteKey = options.siteKey;
        if (options.theme !== undefined) props.theme = options.theme;
        if (options.size !== undefined) props.size = options.size;
        if (options.appearance !== undefined) props.appearance = options.appearance;
        if (options.autoCloseDelay !== undefined) props.autoCloseDelay = options.autoCloseDelay;

        return h(CloudflareVerifyDialog, props);
      },
    });

    // 挂载应用
    app.mount(container);
  });
}

// 常用快捷验证方法

/** 登录验证 - 用户登录时的安全验证 */
export function verifyLogin(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.LOGIN_SUBMIT,
  });
}

/** 注册验证 - 用户注册账户时的安全验证 */
export function verifyRegister(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.REGISTER_SUBMIT,
  });
}

/** 提款验证 - 用户申请提款时的安全验证 */
export function verifyWithdrawal(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.WITHDRAWAL_SUBMIT,
  });
}

// 用户认证验证方法

/** 忘记密码验证 - 用户重置密码时的安全验证 */
export function verifyForgetPassword(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.FORGET_PW_SUBMIT,
  });
}

// 账户管理验证方法

/** 修改密码验证 - 用户修改登录密码时的安全验证 */
export function verifyChangePassword(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.MODIFY_LOGIN_PW_SUBMIT,
  });
}

/** 绑定手机验证 - 用户绑定手机号时的安全验证 */
export function verifyBindPhone(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.BIND_PHONE_SUBMIT,
  });
}

/** 修改手机验证 - 用户更换手机号时的安全验证 */
export function verifyChangePhone(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.MODIFY_PHONE_SUBMIT,
  });
}

// 财务相关验证方法

/** KYC验证 - 用户提交身份认证资料时的安全验证 */
export function verifyKYC(): Promise<VerifyResult> {
  return showCloudflareVerify({
    cfType: CF_TURNSTILE_TYPE.KYC_SUBMIT,
  });
}

// 高级验证功能 无感验证-无弹窗
export function executeSeamlessVerification(options: VerifyOptions): Promise<VerifyResult> {
  return new Promise((resolve) => {
    // 创建隐藏的容器元素用于 Invisible 模式
    const container = document.createElement("div");
    container.style.position = "fixed";
    container.style.left = "-1000px";
    container.style.top = "-1000px";
    container.style.width = "300px";
    container.style.height = "200px";
    container.style.opacity = "0";
    container.style.pointerEvents = "none";
    container.style.zIndex = "-1";
    document.body.appendChild(container);

    // 无感验证需要设置为 true 才能初始化
    const visible = ref(true);

    // 清理函数
    const cleanup = () => {
      setTimeout(() => {
        if (container && container.parentNode) {
          app.unmount();
          container.parentNode.removeChild(container);
        }
      }, 100);
    };

    // 处理验证成功
    const handleSuccess = (result: TurnstileResult) => {
      cleanup();
      resolve({
        success: true,
        "cf-token": result["cf-token"],
        "cf-scene": result["cf-scene"],
        // 保持向后兼容
        token: result["cf-token"],
        cfType: result["cf-scene"],
      });
    };

    // 处理验证失败
    const handleError = (error: string) => {
      cleanup();

      // 如果是 TurnstileResult 对象，直接使用其信息
      if (typeof error === "object" && error !== null) {
        const result = error as any;
        resolve({
          success: false,
          "cf-scene": result["cf-scene"] || options.cfType,
          error: result.error,
          errorCode: result.errorCode,
          retryable: result.retryable,
          // 保持向后兼容
          cfType: result["cf-scene"] || options.cfType,
        });
      } else {
        // 字符串错误，使用默认处理
        resolve({
          success: false,
          "cf-scene": options.cfType,
          error: typeof error === "string" ? error : "Verification failed",
          // 保持向后兼容
          cfType: options.cfType,
        });
      }
    };

    // 处理取消（Invisible 模式通常不会有取消操作）
    const handleCancel = () => {
      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        cancelled: true,
        // 保持向后兼容
        cfType: options.cfType,
      });
    };

    // 无感验证的配置选项
    const seamlessOptions = {
      ...options,
      // 使用 interaction-only 模式实现无感验证
      appearance: "interaction-only" as const,
      size: "compact" as const, // 使用 compact 尺寸减少视觉影响
      theme: options.theme || "light",
      // 无感验证不显示任何 UI，移除多余的 title 和 description
      showCancelButton: false,
      autoCloseDelay: 0,
    };

    // 创建 Vue 应用实例用于 Invisible 验证
    const app = createApp({
      render() {
        // 构建组件属性，只传递有值的属性
        const props: any = {
          modelValue: visible.value,
          "onUpdate:modelValue": (value: boolean) => {
            visible.value = value;
          },
          cfType: seamlessOptions.cfType,
          onSuccess: handleSuccess,
          onError: handleError,
          onCancel: handleCancel,
        };

        // 只有当属性有值时才添加到 props 中
        if (seamlessOptions.siteKey !== undefined) props.siteKey = seamlessOptions.siteKey;
        if (seamlessOptions.theme !== undefined) props.theme = seamlessOptions.theme;
        if (seamlessOptions.size !== undefined) props.size = seamlessOptions.size;
        if (seamlessOptions.appearance !== undefined) props.appearance = seamlessOptions.appearance;
        if (seamlessOptions.showCancelButton !== undefined)
          props.showCancelButton = seamlessOptions.showCancelButton;
        if (seamlessOptions.autoCloseDelay !== undefined)
          props.autoCloseDelay = seamlessOptions.autoCloseDelay;

        return h(CloudflareVerifyDialog, props);
      },
    });

    // 挂载到隐藏容器，启动 Invisible 验证
    app.mount(container);

    // 添加超时处理，防止无限等待
    setTimeout(() => {
      if (!container.parentNode) return; // 已经清理过了

      cleanup();
      resolve({
        success: false,
        "cf-scene": options.cfType,
        error: "Verification timeout",
        cfType: options.cfType,
      });
    }, 30000); // 30秒超时
  });
}
