<script setup lang="ts">
import { useKycStore, PHOTO_TYPE } from "@/stores/kyc";
import { NATIONALLITY_ENUM, S_TYPE_ID } from "@/views/kyc/CONSTANT";
import ZUploadPhoto from "@/components/ZUploadPhoto/index.vue";
import { getServerSideImageUrl } from "@/utils/core/tools";

const kycStore = useKycStore();

const { fullFormData } = storeToRefs(kycStore);

const handleCountryConfirm = (e) => {
  fullFormData.value.id_type = "";
  kycStore.handleSelectConfirm("country", e);
};

const getIdTypeEnum = computed(() => {
  if (fullFormData.value.country != "Philippines") {
    return ["Passports", "ACR"];
  }
  return S_TYPE_ID;
});
</script>
<template>
  <div class="form">
    <div class="form-item">
      <label for="country">Country / Region of Document lssuance</label>
      <ZSelect
        title="Country"
        :modelValue="fullFormData.country"
        :selectList="NATIONALLITY_ENUM"
        :fieldNames="{ label: 'country', value: 'country' }"
        @confirm="handleCountryConfirm"
        placeholder="Philippines"
      >
      </ZSelect>
    </div>
    <div class="form-item">
      <label for="id_type">Select Type of ID</label>
      <ZSelect
        title="Select Type of ID"
        :modelValue="fullFormData.id_type"
        :selectList="getIdTypeEnum"
        @confirm="(e) => kycStore.handleSelectConfirm('id_type', e)"
        placeholder="Please select your type of ID"
      >
      </ZSelect>
    </div>
    <ZUploadPhoto
      :modelValue="getServerSideImageUrl(fullFormData.font_side_url)"
      :height="160"
      class="photo-item"
      ref="idPhotoRef"
      label="Government-Issued ID"
      :required="true"
      @confirm="
        (fontSideUrl) => kycStore.updateDetailPhotoBase64(PHOTO_TYPE.UP_ID_PHOTO, fontSideUrl)
      "
      photoLabel="front side of your ID card"
    ></ZUploadPhoto>
    <ZUploadPhoto
      :modelValue="getServerSideImageUrl(fullFormData.selfie_picture_url)"
      :height="160"
      class="photo-item"
      ref="holdingPhotoRef"
      label="Selfie Holding Valid ID"
      :required="true"
      @confirm="
        (fontSideUrl) =>
          kycStore.updateDetailPhotoBase64(PHOTO_TYPE.UP_ID_PHOTO_HOLDING, fontSideUrl)
      "
      photoLabel="selfie holding valid ID"
    ></ZUploadPhoto>
    <div class="form-item">
      <div class="tips">
        <div class="title">Please upload a photo of your identification card.</div>
        <div class="desc">
          <p>· Please upload a photo of your identification card.</p>
          <p>· Provide a clear, complete photo of the ID.</p>
          <p>· Avoid reflections, shadows, and excessive brightness.</p>
          <p>· Ensure no part of the ID is covered.</p>
          <p>· Place the ID on a flat surface when taking the photo.</p>
          <p>· Use a valid and undamaged ID Only valid IDs from 21+ are accepte</p>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.form {
  .photo-container {
    width: 330px;
    height: 160px;
    overflow: hidden;
    position: relative;
    margin: 0 auto;
    border-radius: 20px;
    cursor: pointer;

    .photo-img {
      position: absolute;
      width: 160px;
      height: 330px;
      transform-origin: 0 0;
      transform: rotate(90deg) translateY(-100%);
      object-fit: fill;
    }
  }

  .photo-upload {
    display: flex;
    height: 160px;
    width: 100%;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
    align-self: stretch;
    border-radius: 20px;
    background: #f4f7fd;
    cursor: pointer;
  }

  .tips {
    .title {
      color: #222;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-top: 10px;
      margin-bottom: 10px;
    }

    .desc {
      color: #999;
      font-family: Inter;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      /* 166.667% */
    }
  }

  .form-item {
    display: flex;
    flex-direction: column;
    margin-bottom: 16px;
    width: 100%;

    label {
      margin-bottom: 8px;
      color: #666;
      font-family: Inter;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
    }
  }
}
</style>
