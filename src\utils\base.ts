import { showToast, closeToast, closeDialog } from "vant";
import { getToken } from "./auth";
import { getEnvConfig } from "./config/Config";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { useGlobalStore } from "@/stores/global";
import { useGameStore } from "@/stores/game";
import { getGlobalDialog } from "@/enter/vant";
import router from "@/router";

// 防重复弹窗标记
let isAuthDialogShowing = false;

// 监听路由变化，重置弹窗标记
router.beforeEach((to, from, next) => {
  // 如果路由发生变化，重置认证弹窗标记
  if (isAuthDialogShowing && to.path !== from.path) {
    isAuthDialogShowing = false;
  }
  next();
});

// 添加通用参数
function withCommonParams(url: string, data: Record<string, any> = {}) {
  return {
    token: getToken(),
    action: url,
    ...data,
  };
}

const getGameProviderNameById = (companyId) => {
  const gameStore = useGameStore();
  const provider = gameStore.vaildThirdCompany;
  if (provider.length <= 0) {
    return "";
  }
  for (let index = 0; index < provider.length; index++) {
    let element = provider[index];
    if (element.id == companyId) {
      return element.provider;
    }
  }
  return "";
};

// 获取基础 URL
export const baseURL = () => getEnvConfig().VITE_API_URL;

/**
 * 需要进行 CF 验证的请求名
 * 这些请求需要将 cf-token 放到请求头中
 */
const CF_TOKEN_CMD_MAP = {
  "/common/api/sms/send/short/msg": true,
  "/common/api/player/login": true,
  "/common/api/set/phone/passwd": true,
  "/common/api/player/add-bind": true,
  "/common/api/update/bind/phone": true,
  "/common/api/is/bind/send": true,
  "/common/api/update/account/info": true,
  "/common/api/add/withdraw/no": true,
};

// 获取请求头
export function getHeaders(config) {
  const globalStore = useGlobalStore();
  const token = getToken();
  const headers: Record<string, string> = config.headers || {};

  let terminal = "128";
  if (globalStore.channel === CHANEL_TYPE.MAYA) {
    terminal = "64";
  } else if (globalStore.channel === CHANEL_TYPE.G_CASH) {
    terminal = "8";
  }
  // 设置终端类型
  headers["terminal"] = terminal;
  // header区分请求来源是Cocos 还是Vue
  headers["source"] = "Vue";
  headers["web-version"] = __APP_VERSION__;

  // 三方游戏登陆接口
  if (["/vdr/api/jili/login"].includes(config.url)) {
    const companyId = config.data?.company_id;
    const name = getGameProviderNameById(companyId) || "";
    let nameTrim = name.replace(/\s+/g, "");
    headers["CaType"] = nameTrim;
  }
  const params = config.data || config.params;
  if (
    CF_TOKEN_CMD_MAP[config.url] &&
    typeof params["cf-token"] === "string" &&
    params["cf-scene"]
  ) {
    // cf 验证信息
    headers["cf-token"] = params["cf-token"];
    headers["cf-scene"] = params["cf-scene"];
  }

  if (globalStore.userInfo && globalStore.userInfo.grayscale) {
    headers["GrayScale"] = globalStore.userInfo.grayscale;
  }
  // 设置 token
  if (token) {
    headers["token"] = token;
  }

  // 合并参数
  if (config.method === "get") {
    config.params = {
      ...withCommonParams(config.url, config.params),
      ...config.params,
    };
  } else if (config.method === "post") {
    config.data = {
      ...withCommonParams(config.url, config.data),
      ...config.data,
    };
  }

  return headers;
}

// 处理 HTTP 错误
export function handleHttpError(res) {
  const { code, data, message, msg } = res;
  const globalStore = useGlobalStore();
  const messageTip = message || msg;

  switch (code) {
    case 426:
      // 显示强制更新弹窗
      import("@/utils/AutoPopMgr").then(({ AutoPopMgr, EnumPopUpPrefabURL }) => {
        import("@/stores/autoPopMgr").then(({ useAutoPopMgrStore }) => {
          const autoPopMgrStore = useAutoPopMgrStore();
          autoPopMgrStore.showForceUpdateDialog = true;
          AutoPopMgr.showPopup(EnumPopUpPrefabURL.ForceUpdate);
        });
      });
      return Promise.reject(new Error(messageTip || "Request error"));
    case 400:
    case 401:
    case 102010:
    case 100010:
    case 401000000:
      const route = router.currentRoute.value;

      // 如果已经在登录页面，直接返回错误
      if (route.path === "/login" || route.name === "Login") {
        globalStore.clearStore();
        closeDialog();
        return Promise.reject(new Error(messageTip || "Unauthorized. Please log in again"));
      }

      // 如果已经有认证弹窗在显示，直接返回错误，不再弹新的窗口
      if (isAuthDialogShowing) {
        globalStore.clearStore();
        return Promise.reject(new Error(messageTip || "Authentication dialog already showing"));
      }

      // 标记认证弹窗正在显示
      isAuthDialogShowing = true;

      const $dialog = getGlobalDialog();
      const msgStr =
        globalStore.channel === CHANEL_TYPE.WEB
          ? "Your login has expired, please log in again."
          : "Since you have not operated on for a long time, please log in again.";

      $dialog({
        title: "Tips",
        message: msgStr,
        showCancelButton: false,
        zIndex: 9999,
        onConfirm: async () => {
          closeDialog();
          // 重置弹窗标记
          isAuthDialogShowing = false;
          globalStore.loginOut();
        },
        onCancel: () => {
          // 重置弹窗标记（虽然这里没有取消按钮，但为了安全起见）
          isAuthDialogShowing = false;
        },
      });

      // 添加安全机制：10秒后自动重置标记，防止意外情况导致标记永远不被重置
      setTimeout(() => {
        if (isAuthDialogShowing) {
          isAuthDialogShowing = false;
        }
      }, 10000);

    case 210001:
      showToast(messageTip || "Request error");
      return Promise.reject(new Error(messageTip || "Request error"));

    default:
      showToast(messageTip || "Request error");
      return Promise.reject(new Error(messageTip || "Request error"));
  }
}
