<script setup lang="ts">
import { computed } from "vue";
import { useRoute } from "vue-router";

import TabBar from "@/components/tabbar/Index.vue";
import DepositDialog from "@/components/DepositDialog/index.vue";
import WithDrawDialog from "@/components/WithDrawDialog/index.vue";
import VerifyDialogPreconditions from "@/components/ZVerifyDialog/VerifyDialogPreconditions.vue";
import KycForm from "@/components/KYCDialog/KycForm.vue";

// utils
import { getRouteKey, keepAliveNames } from "@/utils/RouteCache";

// store
import { useGlobalStore } from "@/stores/global";
import { useKycMgrStore } from "@/stores/kycMgr";

import { useAutoPopMgrStore } from "@/stores/autoPopMgr";

// 浮动
import SpinWheelButton from "@/components/Single-purpose/SpinWheelButton.vue";
import EnvelopeButton from "@/components/Single-purpose/EnvelopeButton.vue";

// 弹窗提示组件
import EnvelopeTip from "@/components/ZPopDialog/EnvelopeTip.vue";
import SpinWheelTip from "@/components/ZPopDialog/SpinWheelTip.vue";
import ForceUpdateDialog from "@/components/ZPopDialog/ForceUpdateDialog.vue";

const autoPopMgrStore = useAutoPopMgrStore();
const kycMgrStore = useKycMgrStore();
const globalStore = useGlobalStore();

const route = useRoute();
const showTabBar = computed(() => route.meta.showTabBar);
const appVersion = __APP_VERSION__;

// 红包小图
const showFloatEnvelopeButton = computed(() => {
  return ["Home"].includes(route.name as string) && autoPopMgrStore.isNeedShowEnvelopePop();
});
// 转盘小图
const ShowSpinWheelButton = computed(() => {
  const showButtonByName = [
    "Home",
    "PromosAndTournament",
    "Message",
    "News",
    "GameCategories",
    "CasinoCate",
  ].includes(route.name as string);
  if (globalStore.token) {
    return showButtonByName && autoPopMgrStore.spinInfo.is_start === 1;
  }
  return showButtonByName;
});
const keepAliveName = keepAliveNames;
const getKey = () => getRouteKey(route);

// 获取环境信息
const envMode = import.meta.env.MODE;

// 获取打包时间（只在构建时才有值）
const buildTime = computed(() => {
  // 开发环境下 __BUILD_TIME__ 可能未定义，只在构建环境显示
  try {
    return typeof __BUILD_TIME__ !== "undefined" ? __BUILD_TIME__ : "";
  } catch {
    return "";
  }
});

// 是否显示调试信息（非生产环境才显示）
const showDebugInfo = computed(() => {
  return envMode !== "production";
});

// 强制更新处理函数
const handleForceUpdate = () => {
  // 强制更新逻辑：刷新页面或跳转到应用商店
  window.location.reload();
};

const handleForceUpdateClose = () => {
  // 强制更新弹窗关闭处理
  autoPopMgrStore.showForceUpdateDialog = false;
};
</script>
<template>
  <div class="h5-app-container">
    <!-- 路由视图：现在启动页也是一个路由页面 -->
    <router-view v-slot="{ Component }">
      <transition name="fade" mode="out-in">
        <keep-alive :include="keepAliveName">
          <component :is="Component" :key="getKey()" />
        </keep-alive>
      </transition>
    </router-view>
    <TabBar v-if="showTabBar" />
    <!-- 充值 -->
    <DepositDialog />
    <!-- 提现 -->
    <WithDrawDialog />
    <!-- kyc弹窗 需要用到的设置手机号、登录密码弹窗 -->
    <VerifyDialogPreconditions
      v-model:showDialog="kycMgrStore.showVerifyPreconditionsDialog"
      :succCallBack="kycMgrStore.handleVerifyPreconditionsSuccess"
    />
    <SpinWheelTip />
    <EnvelopeTip></EnvelopeTip>
    <KycForm></KycForm>
    <!-- 强制更新弹窗 -->
    <ForceUpdateDialog
      v-model="autoPopMgrStore.showForceUpdateDialog"
      :mask-closable="false"
      @update="handleForceUpdate"
      @close="handleForceUpdateClose"
    />

    <!-- 浮动按钮容器 - 确保在 #app 容器内 -->
    <div class="floating-buttons-container">
      <!-- 首冲按钮 -->
      <ZFloatingBubble v-if="showFloatEnvelopeButton" :right="40" :bottom="300">
        <EnvelopeButton />
      </ZFloatingBubble>
      <!-- 大转盘按钮 -->
      <ZFloatingBubble v-if="ShowSpinWheelButton" :right="60" :bottom="220">
        <SpinWheelButton />
      </ZFloatingBubble>
      <!-- 调试信息 - 仅非生产环境显示 -->
      <ZFloatingBubble v-if="showDebugInfo" :right="40" :top="100">
        <div class="develop">
          <div>框架：Vue</div>
          <div>渠道：{{ globalStore.channel }}</div>
          <div>环境：{{ envMode }}</div>
          <div>版本：{{ appVersion }}</div>
          <div v-if="buildTime">{{ buildTime }}</div>
        </div>
      </ZFloatingBubble>
    </div>
  </div>
</template>

<style scoped lang="scss">
.h5-app-container {
  max-width: 480px;
  margin: 0 auto;
  position: relative;
  min-height: 100vh;
  background: #f5f6fa;
}

.floating-buttons-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.develop {
  background-color: rgba($color: #000000, $alpha: 0.5);
  color: #fff;
  padding: 5px 10px;
  border-radius: 10px;
  font-size: 12px;
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  font-family: D-DIN;
}
</style>
