// store/global.ts
import { defineStore } from "pinia";
import { CHANEL_TYPE } from "@/utils/config/GlobalConstant";
import { backhall } from "@/api/games";

import router from "@/router";
import { LoginMgr } from "@/utils/ThirdPartLoginMsg";
import { serviceMgr } from "@/utils/ServiceMgr";
import type {
  UserInfo,
  PayAccount,
  RegisterAward,
  LoginInfo,
  GlobalStoreState,
} from "@/types/store";

export const GLOBAL_STORE = "GLOBAL_STORE";

// 初始状态值
const initValue: GlobalStoreState = {
  token: "",
  channel: CHANEL_TYPE.WEB,
  userInfo: {},
  payAccount: {},
  registerAward: {
    amount: 0,
    type: -1,
  },
  unreadMarks: {},
  // 余额
  balance: 0,
  // 有未读客服消息
  hasUnreadCustomerMsg: false,
  // 有未读站内消息
  hasUnreadIndexboxMsg: false,
  // 有未读新闻
  hasUnreadNews: false,
  loginConfig: {
    // 1关 0开
    login_facebook: 1,
    login_google: 1,
    login_password: 0,
  },
};

export const useGlobalStore = defineStore("global", {
  state: (): GlobalStoreState => ({
    ...initValue,
  }),

  getters: {
    // 是否小程序端，即 G_CASH、MAYA 端
    isMiniChannel(): boolean {
      return ["gcash", "maya"].includes(this.channel?.toLowerCase());
    },
    isMaya() {
      return this.channel?.toLowerCase() === "maya";
    },
    /**
     * 是否已登录
     */
    isLoggedIn: (state): boolean => !!state.token,
  },

  actions: {
    /**
     * 获取用户余额
     */
    async getBalance(): Promise<number> {
      if (!this.token) {
        console.warn("No token available, cannot fetch balance");
        return 0;
      }
      try {
        const result = await backhall();
        const data = result?.data || result || {};
        this.balance = (data.balance || 0) / 100;
        return this.balance;
      } catch (error) {
        console.error("Failed to get balance:", error);
        // 可以选择抛出错误或设置默认值
        this.balance = 0;
        return 0;
      }
    },

    /**
     * 设置登录信息
     * @param info 登录信息
     */
    setLoginInfo(info: Partial<LoginInfo>): void {
      this.token = info.token || "";
      this.userInfo = info.user_info || {};
      this.unreadMarks = info.unread_marks || {};
      this.registerAward = info.register_award || {};
      this.payAccount = info.pay_account || {};
    },

    /**
     * 设置 token
     * @param token 用户令牌
     */
    setToken(token: string): void {
      this.token = token;
    },

    /**
     * 设置渠道
     * @param channel 渠道类型
     */
    setChannel(channel: CHANEL_TYPE): void {
      this.channel = channel;
    },

    /**
     * 更新用户信息
     * @param payload 用户信息更新数据
     */
    updateUserInfo(payload: Partial<UserInfo> = {}): void {
      this.userInfo = { ...this.userInfo, ...payload };
    },

    /**
     * 更新支付账户信息
     * @param payload 支付账户更新数据
     */
    updatePayAccount(payload: Partial<PayAccount>): void {
      this.payAccount = { ...this.payAccount, ...payload };
    },

    /**
     * 更新注册奖励信息
     * @param payload 注册奖励更新数据
     */
    updateRegisterAward(payload: Partial<RegisterAward> = {}): void {
      this.registerAward = { ...this.registerAward, ...payload };
    },

    /**
     * 更新余额
     * @param balance 新余额
     */
    updateBalance(balance: number): void {
      this.balance = balance;
    },
    /**
     * 设置未读新闻状态
     * @param hasUnread 是否有未读新闻
     */
    setUnreadNews(hasUnread: boolean): void {
      this.hasUnreadNews = hasUnread;
    },
    /**
     * 设置未读站内消息状态
     * @param hasUnread 是否有未读站内消息
     */
    setUnreadIndexboxMsg(hasUnread: boolean): void {
      this.hasUnreadIndexboxMsg = hasUnread;
    },

    /**
     * 用户登出
     * @param redirectToLogin 是否重定向到登录页，默认为 true
     */
    async loginOut(redirectToLogin: boolean = true): Promise<void> {
      try {
        // 只清除用户相关的敏感数据，保留配置信息
        this.clearUserData();

        // 客服
        serviceMgr.instance.logout();
        // 重置21岁弹窗
        window["hasShowTip21Old"] = false;

        LoginMgr.instance.google_logout();
        LoginMgr.instance.facebook_logout();

        // 刷新首次访问数据，切换到匿名用户数据
        try {
          const { useFirstVisitStore } = await import("@/stores/firstVisit");
          const firstVisitStore = useFirstVisitStore();
          firstVisitStore.refreshUserData();
        } catch (error) {
          console.warn("Failed to refresh first visit store on logout:", error);
        }

        // 跳转到登录页
        if (redirectToLogin) {
          router.replace("/login");
        }
      } catch (error) {
        console.error("Error during logout:", error);
        // 即使出错也要清除用户数据
        this.clearUserData();
        if (redirectToLogin) {
          router.replace("/login");
        }
      }
    },

    /**
     * 清除用户相关的敏感数据
     * 保留配置信息如 channel、loginConfig 等
     */
    clearUserData(): void {
      // 只清除用户相关的敏感数据
      this.token = "";
      this.userInfo = {};
      this.payAccount = {};
      this.registerAward = {
        amount: 0,
        type: -1,
      };
      this.unreadMarks = {};
      this.balance = 0;
      this.hasUnreadCustomerMsg = false;
      this.hasUnreadIndexboxMsg = false;
      this.hasUnreadNews = false;
      // 保留 channel 和 loginConfig 等配置信息
    },

    /**
     * 清空 store 数据
     * 重置所有状态到初始值
     */
    clearStore(): void {
      Object.assign(this, { ...initValue });
    },

    /**
     * 安全地获取用户信息字段
     * @param field 字段名
     * @param defaultValue 默认值
     * @returns 字段值或默认值
     */
    getUserInfoField<T = any>(field: string, defaultValue: T): T {
      return this.userInfo[field] ?? defaultValue;
    },
  },
  persist: {
    key: GLOBAL_STORE,
    storage: window.localStorage,
  },
});
