/**
 * 登录相关 Store
 */

import { defineStore } from "pinia";
import { showToast } from "vant";
import router from "@/router";
import { useGlobalStore } from "@/stores/global";

import {
  executeVerification,
  type VerificationResult,
  VerificationMgr,
  VERIFICATION_TYPE,
} from "@/utils/VerificationMgr";
import { Md5 } from "@/utils/core/Md5";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import { loginManager } from "@/utils/managers/LoginManager";
import { showZLoading, closeZLoading } from "@/utils/ZLoadingAPI";
import { LoginMgr } from "@/utils/ThirdPartLoginMsg";
// CF 校验通过 VerificationMgr 统一处理，不需要直接导入

import type {
  LoginStoreState,
  PasswordLoginPayload,
  CodeLoginPayload,
  VerificationCallbackResult,
  LoginType,
  VerificationCallback,
  PasswordLoginParams,
  CodeLoginParams,
} from "@/types/login";

const LOGIN_STORE_START = "LOGIN_TYPE_";

export const useLoginStore = defineStore("login", {
  state: (): LoginStoreState => ({
    userPhone: "",
    currentLoginMode: "code", // "password" | 'code'  无记录首次使用验证码登录，有过密码记录或者密码登录默认使用密码登录
    isCodeInputMode: false,
    isPrivacyAgreed: true,
    isPrivacyDialogVisible: false,
    isLoggingIn: false,
  }),

  getters: {
    /**
     * 是否应该显示关闭按钮
     */
    shouldShowCloseButton(): boolean {
      return (
        this.currentLoginMode === "password" ||
        (this.currentLoginMode === "code" && !this.isCodeInputMode)
      );
    },

    /**
     * 是否应该显示返回按钮
     */
    shouldShowBackButton(): boolean {
      return this.isCodeInputMode;
    },

    /**
     * 是否有第三方登录选项
     */
    hasThirdPartyLogin(): boolean {
      const globalStore = useGlobalStore();
      return (
        globalStore.loginConfig.login_facebook == 0 || globalStore.loginConfig.login_google == 0
      );
    },
  },

  actions: {
    /**
     * 初始化登录页面
     */
    initLoginPage() {
      const globalStore = useGlobalStore();
      // 重置状态
      globalStore.loginOut(false);

      // 恢复手机号
      const phoneNum = getLocalStorage("phone");
      const { userInfo } = globalStore;
      this.currentLoginMode = getLocalStorage(LOGIN_STORE_START + phoneNum);
      // 不支持密码登录或者未设置登录密码 默认为code,其余情况默认保持原记录的登录方式
      if (globalStore.loginConfig.login_password !== 1 || userInfo?.login_password !== 1) {
        this.currentLoginMode = "code";
      }

      // 初始化第三方登录SDK
      this.initThirdPartySDK();

      // 恢复手机号
      if (phoneNum) {
        this.userPhone = phoneNum;
      }
      // 重置下验证码登录的步骤
      this.isCodeInputMode = false;
    },

    /**
     * 初始化第三方登录SDK
     */
    initThirdPartySDK() {
      const globalStore = useGlobalStore();

      if (globalStore.loginConfig.login_facebook == 0) {
        LoginMgr.instance.facebook_init();
      }
      if (globalStore.loginConfig.login_google == 0) {
        LoginMgr.instance.google_init();
      }
    },

    /**
     * 处理验证 (支持 Geetest 和 Cloudflare)
     * @param loginType 登录类型
     * @param callback 验证回调
     * @param useSeamless 是否使用无感验证 (仅对 Cloudflare 有效)
     * 验证码的是有感的，提交的部分是无感的
     */
    async handleVerification(
      loginType: LoginType = "phone_login_code",
      callback: VerificationCallback,
      useSeamless: boolean = false
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        // 使用统一的验证管理器
        await VerificationMgr.instance.verify(
          loginType,
          async (result) => {
            if (result && result.success) {
              // 根据验证类型处理结果
              if (result.type === VERIFICATION_TYPE.GEETEST) {
                // Geetest 验证结果
                await callback({
                  buds: result.data?.buds || "64",
                  geetest_guard: result.data?.geetest_guard || "",
                  userInfo: result.data?.userInfo || "",
                  geetest_captcha: result.data?.geetest_captcha || "",
                });
              } else if (result.type === VERIFICATION_TYPE.CLOUDFLARE) {
                // Cloudflare 验证结果 - 转换为 Geetest 格式以保持兼容性
                await callback({
                  // 添加 Cloudflare 特有的字段
                  "cf-token": result.data?.[`cf-token`] || result.data?.cf_token || "",
                  "cf-scene": result.data?.[`cf-scene`] || result.data?.cf_type || loginType,
                });
              }
            } else {
              console.warn("Verification failed or was cancelled");
              showToast("Verification failed");
            }
          },
          {
            phone: this.userPhone, // 传递手机号给 Geetest
          }
        );
      } catch (error) {
        console.error("Verification failed:", error);
        showToast("Verification failed");
      }
    },

    /**
     * 处理 Geetest 验证 (直接使用 GeetestMgr)
     */
    async handleGeetestVerification(
      loginType: LoginType = "phone_login_code",
      callback: VerificationCallback
    ): Promise<void> {
      // 检查隐私协议
      if (!this.isPrivacyAgreed && ["phone_code_login", "password_login"].includes(loginType)) {
        this.isPrivacyDialogVisible = true;
        return;
      }

      try {
        await executeVerification(loginType, async (result: VerificationResult | false) => {
          if (result && result.success) {
            await callback({
              // Geetest 参数
              buds: result.data?.buds || "64",
              geetest_guard: result.data?.geetest_guard || "",
              userInfo: result.data?.userInfo || "",
              geetest_captcha: result.data?.geetest_captcha || "",
              // Cloudflare 参数
              "cf-token": result.data?.["cf-token"] || "",
              "cf-scene": result.data?.["cf-scene"] || "",
            });
          } else {
            console.warn("Verification failed or was cancelled");
            showToast("Verification failed");
          }
        });
      } catch (error) {
        console.error("Geetest verification failed:", error);
        showToast("Verification failed");
      }
    },
    // 记录登录方式为密码
    setLoginTypeTag(type = "code", phone?: string) {
      if (phone || this.userPhone)
        setLocalStorage(LOGIN_STORE_START + (phone || this.userPhone), type);
    },

    /**
     * 执行用户登录
     */
    async executePlayerLogin(
      payload: PasswordLoginParams | CodeLoginParams | Record<string, any>
    ): Promise<void> {
      try {
        this.isLoggingIn = true;
        showZLoading();

        // 执行 CF 校验 - 通过 VerificationMgr 统一处理（根据 SITE_KEY_MAP 自动判断有感/无感）
        console.log("🔐 Starting CF verification for login");

        await new Promise<void>((resolve, reject) => {
          VerificationMgr.instance.verify(
            "phone_code_login", // 使用登录场景，会自动映射到 SCENE_LOGIN（无感验证）
            async (result) => {
              if (result && result.success) {
                console.log("✅ CF verification successful");

                // 将 CF 校验结果添加到登录参数中
                const loginPayload = {
                  ...payload,
                  "cf-token": result.data?.["cf-token"] || result.data?.cf_token || "",
                  "cf-scene": result.data?.["cf-scene"] || result.data?.cf_type || "",
                  // 向后兼容的旧属性名
                  cf_token: result.data?.["cf-token"] || result.data?.cf_token || "",
                  cf_type: result.data?.["cf-scene"] || result.data?.cf_type || "",
                };

                try {
                  await loginManager.executeLogin(loginPayload);
                  resolve();
                } catch (error) {
                  reject(error);
                }
              } else {
                console.error("❌ CF verification failed:", result);
                const errorMessage =
                  result && typeof result === "object" && "error" in result
                    ? result.error
                    : "verification failed";
                reject(new Error(errorMessage || "verification failed"));
              }
            }
          );
        });
      } catch (error) {
        console.error("Player login failed:", error);
        throw error;
      } finally {
        this.isLoggingIn = false;
        closeZLoading();
      }
    },

    /**
     * 处理密码登录
     */
    async handlePasswordLogin(payload: PasswordLoginPayload): Promise<void> {
      const { password, phone: phoneNum } = payload;

      if (!password) {
        throw new Error("Password is required");
      }

      return new Promise((resolve, reject) => {
        this.handleVerification(
          "password_login",
          async (verificationResult: VerificationCallbackResult) => {
            try {
              const loginParams: PasswordLoginParams = {
                login_type: "phone",
                phone: phoneNum || this.userPhone,
                password: Md5.hashStr(password).toString(),
                ...(verificationResult || {}),
              };
              await this.executePlayerLogin(loginParams);
              this.setLoginTypeTag("password");
              resolve();
            } catch (error) {
              reject(error);
            }
          },
          true // 使用无感验证
        );
      });
    },

    /**
     * 处理验证码登录
     */
    async handleCodeLogin(payload: CodeLoginPayload): Promise<void> {
      const { verCode, phone: phoneNum } = payload;

      return new Promise((resolve, reject) => {
        this.handleVerification(
          "phone_code_login",
          async (verificationResult: VerificationCallbackResult) => {
            try {
              const loginParams: CodeLoginParams = {
                login_type: "phone",
                phone: phoneNum || this.userPhone,
                verifyCode: verCode,
                ...(verificationResult || {}),
              };
              await this.executePlayerLogin(loginParams);
              this.setLoginTypeTag("code");
              resolve();
            } catch (error) {
              reject(error);
            }
          },
          true // 使用无感验证
        );
      });
    },

    /**
     * 处理Google登录
     */
    tapGoogleLogin() {
      LoginMgr.instance.google_login();
    },

    async handleGoogleLogin(idToken?: string) {
      const params = {
        googleToken: idToken,
        login_type: "google",
        google_redirect_uri: import.meta.env.VITE_WEB_URL,
        token: undefined,
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 处理Facebook登录
     */
    tapFacebookLogin() {
      LoginMgr.instance.facebook_login();
    },
    async handleFacebookLogin(accessToken?: string, userID?: string) {
      const params = {
        accessToken: accessToken,
        faceUserId: userID,
        login_type: "facebook",
        token: undefined,
      };
      await this.executePlayerLogin(params);
    },

    /**
     * 切换到验证码登录
     */
    switchToCodeLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "code";
      setLocalStorage(LOGIN_STORE_START + phoneNum, "code");
    },

    /**
     * 切换到密码登录
     */
    switchToPasswordLogin(phoneNum: string) {
      this.userPhone = phoneNum;
      this.currentLoginMode = "password";
      setLocalStorage(LOGIN_STORE_START + phoneNum, "password");
    },

    /**
     * 处理验证码页面变化
     */
    handleCodePageChange(showCodeInput: boolean) {
      this.isCodeInputMode = showCodeInput;
    },

    /**
     * 处理返回操作
     */
    handleGoBack() {
      this.isCodeInputMode = false;
    },

    /**
     * 处理关闭页面
     */
    handleClosePage() {
      router.replace("/home");
      // router.back();
    },

    /**
     * 处理页面导航
     */
    handleNavigateToPage(path: string) {
      router.push(path);
    },

    /**
     * 处理隐私协议状态变化
     */
    handleAgreementChange(accepted: boolean) {
      this.isPrivacyAgreed = accepted;
    },

    /**
     * 处理隐私政策确认
     */
    handlePrivacyConfirm() {
      this.isPrivacyAgreed = true;
      this.isPrivacyDialogVisible = false;
    },

    /**
     * 重置登录状态
     */
    resetLoginState() {
      this.isCodeInputMode = false;
      this.isPrivacyDialogVisible = false;
      this.isLoggingIn = false;
    },
  },
});
