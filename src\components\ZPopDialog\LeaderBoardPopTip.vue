<!-- filepath: /src/components/ZPopDialog/LeaderBoardPopTip.vue -->
<template>
  <ZPopOverlay :show="showLeaderBoardPopTip">
    <div class="content" :style="{ backgroundImage: `url(${rankCasinoImage})` }">
      <!-- 标题部分 -->
      <div class="date">{{ popupTitle }}</div>
      <!-- 排行榜部分 -->
      <div class="rank">
        <div v-for="(item, index) in rankList" :key="item.player_id" :class="'rank' + index">
          <div :class="'userid rank_userid' + index">{{ item.player_id }}</div>
          <div :class="'bet rank_bet' + index">{{ item.total_bet_amount }}</div>
          <div :class="'bonus rank_bonus' + index">{{ item.award }}</div>
        </div>
      </div>
      <!-- 详情按钮 -->
      <div class="btn">
        <GradientButton
          :showLight="true"
          background-gradient="linear-gradient(135deg, #EB3DC2 20.59%, #7066FE 94.85%)"
          border-gradient="linear-gradient(85deg, #F1D6FF 0%,  #F1D6FF 100%)"
          @click="handleDetail"
          >Details</GradientButton
        >
      </div>
      <!-- 关闭按钮 -->
      <div class="close" @click="handleClose">
        <ZIcon type="icon-guanbi2" color="#fff" :size="28" />
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import { storeToRefs } from "pinia";
import { useAutoPopMgrStore, STORE_KEY_MAP } from "@/stores/autoPopMgr";
import { AutoPopMgr } from "@/utils/AutoPopMgr";
import { amountFormatThousands, isNumeric, getToday } from "@/utils/core/tools";
import { setLocalStorage, getLocalStorage } from "@/utils/core/Storage";
import router from "@/router";
// 导入图片资源，确保与预加载使用相同的路径
import rankCasinoImage from "@/assets/images/popDialog/rank_casino.png";

// store 引用
const autoPopStore = useAutoPopMgrStore();
const { showLeaderBoardPopTip, leaderBoardPopInfo } = storeToRefs(autoPopStore);

// 数据状态
const rankList = ref<any[]>([]);
const popupTitle = ref("");

// 处理排行榜数据，取前三名并格式化金额
const processRankList = (list: any[]) => {
  const result: any[] = [];
  list?.forEach((item, index) => {
    if (index < 3) {
      result.push({
        ...item,
        total_bet_amount: isNumeric(item.total_bet_amount)
          ? amountFormatThousands(item.total_bet_amount)
          : item.total_bet_amount,
        award: isNumeric(item.award) ? amountFormatThousands(item.award) : item.award,
      });
    }
  });
  rankList.value = result;
};

// 格式化标题，如 "MAR.15 TOP 3 WINNERS"
const formatPopupTitle = (dateStr: string) => {
  // console.log('Casino formatPopupTitle 接收到的日期:', dateStr);
  let dateString = dateStr || getToday();
  const dateObj = new Date(dateString);
  const monthAbbreviations = [
    "JAN",
    "FEB",
    "MAR",
    "APR",
    "MAY",
    "JUN",
    "JUL",
    "AUG",
    "SEP",
    "OCT",
    "NOV",
    "DEC",
  ];
  const month = monthAbbreviations[dateObj.getMonth()];
  const day = dateObj.getDate();
  const formattedTitle = `${month}.${day} TOP 3 WINNERS`;
  // console.log('Casino formatPopupTitle 格式化后的标题:', formattedTitle);
  popupTitle.value = formattedTitle;
};

// 记录每日进入排行榜弹窗的信息
const recordEntry = () => {
  const currentDate = new Date();
  const currentDateStr = `${currentDate.getFullYear()}-${
    currentDate.getMonth() + 1
  }-${currentDate.getDate()}`;
  const lastEntry = getLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME) || "";
  if (!lastEntry) {
    setLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME, currentDateStr + "_1");
  } else {
    const [, countStr] = lastEntry.split("_");
    const newCount = parseInt(countStr) + 1;
    setLocalStorage(STORE_KEY_MAP.RANK_LAST_ENTER_TIME, currentDateStr + "_" + newCount);
  }
};

// 点击详情按钮，关闭弹窗并跳转到详情页
const handleDetail = () => {
  showLeaderBoardPopTip.value = false;
  router.push("/promos/promo_5");
};

// 点击关闭按钮，关闭弹窗并销毁当前弹窗状态
const handleClose = () => {
  showLeaderBoardPopTip.value = false;
  AutoPopMgr.destroyCurrentPopup();
};

// 记录进入弹窗次数
watch(
  () => autoPopStore.showLeaderBoardPopTip,
  (show) => {
    console.log("Casino弹窗显示状态变化:", show);
    if (show) {
      recordEntry();
    }
  }
);

// 当排行榜数据更新时，处理数据并更新标题
watch(
  () => autoPopStore.leaderBoardPopInfo,
  (data: any) => {
    if (data && data.list && data.list.rank) {
      processRankList(data.list.rank);
      formatPopupTitle(data.list.time);
    }
  },
  { deep: true, immediate: true }
);
</script>

<style lang="scss" scoped>
.content {
  width: 280px;
  height: 400px;
  padding: 20px;
  border-radius: 33px;
  box-sizing: border-box;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  z-index: 9;

  .rank {
    position: absolute;
    top: 132px;
    left: 130px;

    .userid,
    .bet,
    .bonus {
      font-family: "D-DIN";
      font-size: 12px;
      font-weight: 700;
      line-height: normal;
      height: 18px;
    }

    .userid {
      padding-left: 10px;
    }

    .bet,
    .bonus {
      padding-left: 33px;
    }

    .rank0 {
      color: #ffae00;
    }

    .rank1 {
      margin-top: 17px;
      color: #7cadd5;
    }

    .rank2 {
      margin-top: 17px;
      color: #ee9e59;
    }
  }

  .date {
    width: 100%;
    position: absolute;
    top: 94px;
    left: 50%;
    transform: translateX(-50%);
    color: #fff;
    text-align: center;
    font-family: "Inter";
    font-size: 11px;
    font-weight: 500;
  }

  .btn {
    width: 180px;
    height: 30px;
    box-sizing: border-box;
    position: absolute;
    top: 342px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }

  .close {
    position: absolute;
    top: 410px;
    left: 50%;
    transform: translateX(-50%);
    cursor: pointer;
  }
}
</style>
