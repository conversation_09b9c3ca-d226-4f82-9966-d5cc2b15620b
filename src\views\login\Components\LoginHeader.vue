<!--
  登录页面头部导航组件
  负责显示关闭按钮和返回按钮
-->
<template>
  <div class="login-header">
    <!-- 关闭按钮 -->
    <ZIcon
      v-if="loginStore.shouldShowCloseButton"
      @click="handleClose"
      type="icon-close"
      color=""
      class="header-button close-button"
    ></ZIcon>
    <!-- 返回按钮 -->
    <ZIcon
      v-if="loginStore.shouldShowBackButton"
      @click="handleBack"
      type="icon-fanhui"
      color=""
      class="header-button back-button"
    ></ZIcon>
  </div>
</template>

<script setup lang="ts">
/**
 * 登录页面头部导航组件
 */
import { useLoginStore } from "@/stores/login";

// 使用 login store
const loginStore = useLoginStore();

/**
 * 处理关闭按钮点击
 */
const handleClose = () => {
  loginStore.handleClosePage();
};

/**
 * 处理返回按钮点击
 */
const handleBack = () => {
  loginStore.handleGoBack();
};
</script>

<style scoped lang="scss">
.login-header {
  position: relative;
  width: 100%;

  .header-button {
    position: absolute;
    top: 10px;
    color: #909090;
    cursor: pointer;
    z-index: 1;
  }

  .close-button {
    right: 20px;
  }

  .back-button {
    left: 20px;
  }
}
</style>
