<!-- 手机号验证通用弹窗 -->
<template>
  <div>
    <ZActionSheet
      v-model="visible"
      :title="dialogTitle"
      confirmText="Done"
      :showCancelButton="false"
      :onConfirm="handleConfirm"
      :onCancel="handleClose"
    >
      <div class="dialog-content">
        <div class="send-code-tip" v-show="hasSentCode || passPhone">
          A text message with a 6-digit code was just sent to
          <b>{{ formattedUserPhone }}</b>
        </div>
        <div class="phone-input-container">
          <div class="verCode">Enter a verification code</div>
          <div class="phone-input-code">
            <VerificationCodeInput
              ref="verificationCodeRef"
              class="verificationCode"
              v-model="verificationCode"
            />
            <CodeCountdownButton ref="countdownButtonRef" @click="checkPhoneIsRegister" />
          </div>
        </div>
      </div>
    </ZActionSheet>
    <!-- 更改手机号 -->
    <template v-if="verifyType === PN_VERIFY_TYPE.ChangePhoneNumber">
      <SetChangePhoneDialog
        v-model="showNextDialog"
        :verifyType="PN_VERIFY_TYPE.ChangePhoneNumber"
        @complete="showNextDialog = false"
      >
      </SetChangePhoneDialog>
    </template>
    <!-- 更新支付密码 -->
    <template v-if="verifyType === PN_VERIFY_TYPE.ChangePaymentPassword">
      <SetPaymentPasswordDialog
        v-model="showNextDialog"
        :succCallBack="succCallBack"
        @complete="showNextDialog = false"
      >
      </SetPaymentPasswordDialog>
    </template>
    <!-- 更新登陆密码 -->
    <template v-if="verifyType === PN_VERIFY_TYPE.ForgetPassword">
      <SetLoginPasswordDialog
        v-model="showNextDialog"
        :phone="passPhone"
        @complete="showNextDialog = false"
        :succCallBack="succCallBack"
      ></SetLoginPasswordDialog>
    </template>
  </div>
</template>

<script setup lang="ts">
import { sendCodeMsg, verifyCode, isBindSend } from "@/api/setPhoneNumber";
import { GEETEST_TYPE } from "@/utils/GeetestMgr";
import { executeVerification, type VerificationResult } from "@/utils/VerificationMgr";
import { useGlobalStore } from "@/stores/global";
import { ref, computed } from "vue";
import { showToast } from "vant";
import { PN_VERIFY_TYPE, getPNTitleByType } from "./types";
import { GlobalEnum } from "@/utils/config/GlobalEnum";
import SetChangePhoneDialog from "./SetChangePhoneDialog.vue";
import SetPaymentPasswordDialog from "./SetPaymentPasswordDialog.vue";
import SetLoginPasswordDialog from "./SetLoginPasswordDialog.vue";
import CodeCountdownButton from "@/components/CodeCountdownButton.vue";
import VerificationCodeInput from "@/components/VerificationCodeInput/index.vue";
import { handleSmsResponse } from "@/utils/responseProcessing";
import { formatPhoneNumber } from "@/utils/core/tools";

import enTranslations from "@/utils/I18n/en.json";

const globalStore = useGlobalStore();
const { userInfo } = storeToRefs(globalStore);
const countdownButtonRef = ref();

const props = defineProps({
  // 显示弹窗
  showDialog: {
    type: Boolean,
    default: false,
    required: false,
  },
  // 弹窗确认
  onConfirm: {
    type: Function,
    default: () => {},
    required: false,
  },
  // 弹窗关闭
  onCancel: {
    type: Function,
    default: () => {},
    required: false,
  },
  //  认证类型
  verifyType: {
    type: Number,
    default: -1,
    required: true,
  },
  // 传递的手机号，忘记密码场景
  passPhone: {
    type: String,
    default: "",
    required: false,
  },
  // 成功回调
  succCallBack: {
    type: Function,
    default: () => {},
    required: false,
  },
});

// 弹窗是否显示
const visible = ref(props.showDialog);
// 下一个的弹窗
const showNextDialog = ref(false);
// 手机号
const phone = ref("");
// 原手机号
const oldPhone = ref("");
// 验证码
const verificationCode = ref("");
const verificationCodeRef = ref();

// 是否已发送验证码
const hasSentCode = ref(false);

const smsType = ref(1);

const dialogTitle = computed(() => {
  return getPNTitleByType(props.verifyType);
});

/**
 * 监听modelValue变化
 */
watch(
  () => props.showDialog,
  (val) => {
    visible.value = val;
    if (val) {
      console.log("ZVerifyDialog: 初始化弹窗");
      init();
    } else {
      console.log("ZVerifyDialog: 重置弹窗数据");
      resetData();
    }
  }
);

/**
 * 监听 visible 变化，同步到父组件
 */
watch(
  () => visible.value,
  (val) => {
    if (val !== props.showDialog) {
      emit("update:showDialog", val);
    }
  }
);

const resetData = () => {
  console.log("ZVerifyDialog: 重置所有数据");
  phone.value = "";
  oldPhone.value = "";
  verificationCode.value = "";
  hasSentCode.value = false;
  verificationCodeRef.value?.clear();
};

const init = () => {
  if (
    [PN_VERIFY_TYPE.SetPaymentPassword, PN_VERIFY_TYPE.ChangePaymentPassword].includes(
      props.verifyType
    )
  ) {
    if (userInfo.value.phone) {
      phone.value = userInfo.value.phone;
    }
  } else if (props.verifyType == PN_VERIFY_TYPE.ForgetPassword) {
    phone.value = props.passPhone || userInfo.value.phone;
  } else if (props.verifyType == PN_VERIFY_TYPE.ChangePhoneNumber) {
    oldPhone.value = props.passPhone || userInfo.value.phone;
    phone.value = props.passPhone || userInfo.value.phone;
  }
};

//发送验证码之前先 geetest
const checkPhoneIsRegister = () => {
  // 调用方，一定要正确传 ‘verifyType’
  if (props.verifyType === -1) return;
  let gtype = "";
  switch (props.verifyType) {
    case PN_VERIFY_TYPE.ChangePhoneNumber:
      gtype = GEETEST_TYPE.change_pt_phone_code;
      break;
    case PN_VERIFY_TYPE.SetPhoneNumber:
      gtype = GEETEST_TYPE.bind_pt_phone_code;
      break;
    case PN_VERIFY_TYPE.AddWithdrawAccount:
      gtype = GEETEST_TYPE.bind_withdraw_account_code;
      break;
    case PN_VERIFY_TYPE.ChangeWithdrawAccount:
      gtype = GEETEST_TYPE.change_withdraw_account_code;
      break;
    case PN_VERIFY_TYPE.ForgetPassword:
      gtype = GEETEST_TYPE.forget_password_code;
      break;
    case PN_VERIFY_TYPE.ChangePaymentPassword:
      gtype = GEETEST_TYPE.change_pay_password_code;
      break;
    case PN_VERIFY_TYPE.SetPaymentPassword:
      gtype = GEETEST_TYPE.first_pay_password;
      break;
    case PN_VERIFY_TYPE.SetLoginPassword:
      gtype = GEETEST_TYPE.first_password;
      break;
    case PN_VERIFY_TYPE.ChangeLoginPassword:
      gtype = GEETEST_TYPE.forget_password_code;
      break;
    default:
      break;
  }
  executeVerification(gtype, (result: VerificationResult | false) => {
    if (result && result.success) {
      const ret = {
        // Geetest 参数
        geetest_guard: result.data?.geetest_guard || "",
        userInfo: result.data?.userInfo || "",
        geetest_captcha: result.data?.geetest_captcha || "",
        buds: result.data?.buds || "64",
        // Cloudflare 参数
        "cf-token": result.data?.["cf-token"] || "",
        "cf-scene": result.data?.["cf-scene"] || "",
      };
      checkPhoneIsRegister_true(ret);
    }
  });
};
//发送验证码
const checkPhoneIsRegister_true = async (ret) => {
  let params = {
    phone: phone.value,
    telephoneCode: "+63",
    type: props.verifyType,
    // Geetest 参数
    geetest_guard: ret?.geetest_guard || "",
    userInfo: ret?.userInfo || "",
    geetest_captcha: ret?.geetest_captcha || "",
    buds: ret?.buds || "64",
    // Cloudflare 参数
    "cf-token": ret?.["cf-token"] || "",
    "cf-scene": ret?.["cf-scene"] || "",
  };
  if (
    [
      PN_VERIFY_TYPE.SetPhoneNumber,
      PN_VERIFY_TYPE.SetPaymentPassword,
      PN_VERIFY_TYPE.ChangePhoneNumber,
      PN_VERIFY_TYPE.AddWithdrawAccount,
      PN_VERIFY_TYPE.ChangeWithdrawAccount,
      PN_VERIFY_TYPE.ChangePaymentPassword,
    ].includes(props.verifyType)
  ) {
    if (props.verifyType == PN_VERIFY_TYPE.AddWithdrawAccount) {
      params["type"] = GlobalEnum.SMS_TYPE.BIND_WITHDRAW_ACCOUNT;
    }
    if (props.verifyType == PN_VERIFY_TYPE.ChangeWithdrawAccount) {
      params["type"] = GlobalEnum.SMS_TYPE.UPDATE_WITHDRAW_ACCOUNT;
    }
    if (props.verifyType == PN_VERIFY_TYPE.ChangePhoneNumber) {
      params["type"] = GlobalEnum.SMS_TYPE.UPDATE_PHONE;
    }
    if (props.verifyType == PN_VERIFY_TYPE.SetPhoneNumber) {
      params["type"] = GlobalEnum.SMS_TYPE.BIND_PHONE;
    }
    if (props.verifyType == PN_VERIFY_TYPE.ChangePaymentPassword) {
      params["type"] = GlobalEnum.SMS_TYPE.SETTING_WALLET_PASSWORD;
    }
    if (props.verifyType == PN_VERIFY_TYPE.SetPaymentPassword) {
      params["type"] = GlobalEnum.SMS_TYPE.SETTING_WALLET_PASSWORD;
    }
    smsType.value = params["type"];

    const response = await sendCodeMsg(params);

    handleSmsResponse(response, {
      countdownRef: countdownButtonRef,
      onSuccess: () => {
        hasSentCode.value = true;
        console.error("发送验证码成功");
      },
      onError: (code: any, message: any) => {
        console.error("发送验证码失败:", code, message);
      },
    });
  } else {
    //次数获取验证码既可以绑定手机号也可以修改密码
    let isForgetPw = props.verifyType == PN_VERIFY_TYPE.ForgetPassword;
    params["isBind"] = isForgetPw ? 1 : 0; //需要已经绑定填1  不需要绑定发0
    if (params["isBind"] === 0) {
      const { code, msg } = await isBindSend(params);
      if (code === 200) {
        showToast(enTranslations.sendCodeMsgTip7);
        hasSentCode.value = true;
        if (countdownButtonRef.value) {
          countdownButtonRef.value.start();
        }
      } else {
        if (code === 600) {
          showToast(enTranslations.tipword23);
        } // 验证码发送失败，使用组件内置的失败处理方法
        else if (countdownButtonRef.value) {
          countdownButtonRef.value.handleSendFailed();
        }
      }
    } else {
      smsType.value = GlobalEnum.SMS_TYPE.UPDATE_LOGIN_PASSWORD;
      const response = await sendCodeMsg({
        ...params,
        type: GlobalEnum.SMS_TYPE.UPDATE_LOGIN_PASSWORD,
      });

      handleSmsResponse(response, {
        countdownRef: countdownButtonRef,
        onSuccess: () => {
          hasSentCode.value = true;
        },
        onError: (code: any, message: any) => {
          console.error("sendCodeMsg失败:", code, message);
        },
      });
    }
  }
};

const emit = defineEmits(["update:showDialog"]);

const handleClose = () => {
  emit("update:showDialog", false);
};

const formattedUserPhone = computed(() => {
  const phone = (userInfo.value.phone || props.passPhone || "").toString();
  return formatPhoneNumber(phone);
});

const handleConfirm = async () => {
  if (verificationCodeRef.value?.isValid()) {
    const { code, msg } = await verifyCode({
      phone: phone.value,
      telephoneCode: "+63",
      code: verificationCode.value,
      type: smsType.value,
    });

    if (code === 200 || code === 0) {
      showNextDialog.value = true;
      handleClose();
    } else {
      showToast(msg || "Verification Error,Please Try Again");
    }
  } else {
    verificationCodeRef.value?.validate();
  }
};
</script>

<style scoped lang="scss">
.dialog-content {
  padding-top: 12px;
  padding-bottom: 10px;

  // 验证码步骤样式
  .send-code-tip {
    color: #222;
    font-family: Inter;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    margin-bottom: 16px;

    /* 171.429% */
  }
}
.verCode {
  color: #666;
  font-family: Inter;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
  margin-bottom: 10px;
}
.phone-input-code {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  .verificationCode {
    flex: 1;
    height: 40px;
    border: 1px solid #eee;
    border-radius: 20px;
    padding: 0 12px;
    outline: none;
    background-color: #f4f7fd;
  }
}
</style>
