import vue from "@vitejs/plugin-vue";
import type { ImportMetaEnv } from "../../../env.d.ts";
import type { Plugin } from "vite";
import { configHtmlPlugin } from "./html";
import { configCompressPlugin } from "./compress";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { VantResolver } from "unplugin-vue-components/resolvers";
import vueJsx from "@vitejs/plugin-vue-jsx";
import svgLoader from "vite-svg-loader";
import viteCompression from "vite-plugin-compression";
import { VitePWA } from "vite-plugin-pwa";

export function createVitePlugins(viteEnv: ImportMetaEnv, isBuild: boolean) {
  const plugins: (Plugin | any[])[] = [
    vue(),
    VitePWA({
      // registerType: "autoUpdate",
      registerType: "prompt", // 改为 prompt，不自动注册 Service Worker
      includeAssets: ["favicon.ico", "icon-192.png", "icon-512.png"],
      // 启用 Service Worker 自动注册
      // injectRegister: "auto",
      injectRegister: false,
      manifest: {
        name: "NUSTAR",
        short_name: "NUSTAR",
        description: "",
        theme_color: "#fff",
        start_url: "/", // 启动路径（相对路径，基于部署根目录）
        display: "standalone", // 显示模式（standalone 类似原生应用）
        lang: "en-US",
        dir: "ltr",
        icons: [
          {
            src: "/icon-192.png",
            sizes: "192x192",
            type: "image/png",
          },
          {
            src: "/icon-512.png",
            sizes: "512x512",
            type: "image/png",
          },
          // {
          //   src: "/icon-512.png",
          //   sizes: "512x512",
          //   type: "image/png",
          //   purpose: "any maskable", // 适配 Android 12+ 的 maskable 图标
          // },
        ],
      },
      // devOptions: {
      //   enabled: true, // 开发环境启用 PWA（方便测试）
      //   type: "module", // 使用 modern build
      //   navigateFallback: "index.html",
      // },
      // Service Worker 配置（基于 Workbox）
      // workbox: {
      //   globPatterns: ["**/*.{js,css,html,png,jpg,jpeg,svg,ico,woff2}"],
      //   cleanupOutdatedCaches: true,
      //   clientsClaim: true,
      //   skipWaiting: true,
      //   // 确保正确处理导航请求
      //   navigateFallback: "/index.html",
      //   navigateFallbackDenylist: [/^\/_/, /\/[^/?]+\.[^/]+$/],
      // },
    }),
    vueJsx(), // 自动导入 Vue 相关函数，如 ref, reactive, toRef 等·
    svgLoader({
      defaultImport: "component", // 默认导入为组件
    }), // 关键配置，让 SVG 作为组件导入
    // <img :src="new URL('@/assets/icon.svg', import.meta.url).href" />
    // <img src="/icon.svg" />
    Components({
      dts: true,
      resolvers: [VantResolver()],
      types: [],
    }),
    AutoImport({
      include: [
        /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
        /\.vue$/,
        /\.vue\?vue/, // .vue
      ],
      imports: ["vue", "vue-router", "pinia", "@vueuse/core"],
      dts: "types/auto-imports.d.ts",
    }),
    // 基本配置（Gzip压缩）
    viteCompression({
      // 压缩算法，可选 'gzip' | 'brotliCompress' | 'deflate' | 'deflateRaw'
      algorithm: "gzip",
      // 匹配需要压缩的文件
      filter: /\.(js|css|html|svg)$/,
      // 压缩后是否删除源文件
      deleteOriginFile: false,
      // 只有压缩体积大于该值的文件才会被处理（单位：字节）
      threshold: 10240,
      // 压缩率（0-1），值越小压缩速度越快，压缩率越低
      compressionOptions: { level: 6 },
      // 压缩文件的扩展名
      ext: ".gz",
    }),
  ];

  // VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE 打包使用压缩时是否删除原始文件，默认为 false
  const { VITE_BUILD_COMPRESS, VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE } = viteEnv;

  plugins.push(configHtmlPlugin(viteEnv));

  if (isBuild) {
    // rollup-plugin-gzip
    // 加载 gzip 打包
    const compressType = VITE_BUILD_COMPRESS as "gzip" | "brotli" | "none";
    plugins.push(...configCompressPlugin(compressType, VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE));
  }

  return plugins;
}

// # 启用 gzip 压缩支持
// gzip on;
// gzip_static on;  # 关键：允许服务器直接使用预生成的 .gz 文件

// # 配置支持的文件类型
// gzip_types text/css application/javascript text/javascript application/json text/plain image/svg+xml;

// # 其他优化配置
// gzip_http_version 1.1;
// gzip_vary on;  # 告诉浏览器服务器支持压缩
