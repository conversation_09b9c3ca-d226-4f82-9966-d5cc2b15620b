<template>
  <!-- 通用奖励弹窗：反水奖励、注册奖励等 -->
  <ZPopOverlay :show="show">
    <div class="wrap" :style="{ backgroundImage: `url(${lightBgImg})` }">
      <!-- 顶部礼盒图片 -->
      <img class="head-gift" :src="giftImg" alt="" />
      <!-- 内容外框-border显示 -->
      <div class="content-border">
        <!-- 内容 -->
        <div class="content">
          <!-- 标题 -->
          <img class="head-title" :src="congratulationsImg" alt="" />
          <div>
            <!-- 提示文本 -->
            <div class="tips" :style="{ textAlign: TipsAlign }">{{ tipsText }}</div>
            <!-- 金额 -->
            <div class="bonus-wrap">
              <IconCoin class="icon" :size="40" />
              <span class="bonus">{{ bonusAmount }}</span>
            </div>
          </div>
          <div class="btn" ref="confirmBtnRef">
            <GradientButton
              background-gradient="linear-gradient(180deg, #FF1E35 20.59%, #FF916C 94.85%)"
              border-gradient="#FFDFBF"
              :showLight="true"
              @click="handleClick"
              >{{ buttonText }}</GradientButton
            >
          </div>
          <div class="date" v-if="dateText">{{ dateText }}</div>
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup name="PublicActivityBonusTip">
import { ref, computed } from "vue";
// @ts-ignore
import GradientButton from "@/components/GradientButton/index.vue";
// 导入图片资源，确保与预加载使用相同的路径
import giftImg from "@/assets/images/popDialog/activityBonus-headGift.png";
import lightBgImg from "@/assets/images/popDialog/activityBonus-light.png";
import congratulationsImg from "@/assets/images/popDialog/activityBonus-congratulations.png";

// 定义 props
interface Props {
  show?: boolean;
  tipsText?: string;
  bonusAmount?: string | number;
  dateText?: string;
  buttonText?: string;
  TipsAlign?: string;
}

const props = withDefaults(defineProps<Props>(), {
  show: false,
  tipsText: "You received a bonus:",
  bonusAmount: "0",
  dateText: "",
  buttonText: "Done",
  TipsAlign: "center",
});

const emit = defineEmits<{
  "update:show": [value: boolean];
  "start-coin-animation": [element: HTMLElement | null];
  click: [];
}>();

const confirmBtnRef = ref<HTMLElement | null>(null);

// 处理点击事件
const handleClick = () => {
  emit("click");
  emit("start-coin-animation", confirmBtnRef.value);
  emit("update:show", false);
};

defineExpose({
  confirmBtnRef,
});
</script>

<style lang="scss" scoped>
.wrap {
  width: 100%;
  text-align: center;
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
  position: relative;
  font-family: "Inter";
  .head-gift {
    width: 160px;
    height: auto;
    margin: 0 auto;
    position: absolute;
    top: 95px;
    left: 30%;
  }
  .head-title {
    width: 220px;
    height: auto;
    margin: 0 auto 20px;
  }
  .content-border {
    background: #fff6c6;
    padding: 2px;
    margin: 150px 24px 90px;
    // min-height: 300px;
    border-radius: 33px;
    .content {
      border-radius: 33px;
      padding: 73px 21px 25px;
      background: linear-gradient(180deg, #ffd0d0cb 0%, #fff 30%);
      height: 100%;
      // min-height: 298px;
      width: 100%;
    }
  }
  .tips {
    color: #222;
    text-align: left;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 28px;
  }
  .bonus-wrap {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    margin-top: 16px;
    line-height: 1;
    .bonus {
      color: #222;
      font-family: "D-DIN";
      font-size: 36px;
      font-style: normal;
      font-weight: 700;
    }
  }

  .btn {
    margin: 0 auto;
    margin-top: 36px;
    width: 200px;
  }
  .date {
    margin-top: 8px;
    color: #222;
    text-align: center;
    font-size: 16px;
    font-weight: 400;
    line-height: normal;
  }
}
</style>
