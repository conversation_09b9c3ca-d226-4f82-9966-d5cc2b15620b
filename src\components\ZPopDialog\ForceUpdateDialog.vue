<template>
  <ZPopOverlay :show="showUpdateDialog" @click="handleClose">
    <div class="update-wrap">
      <!-- 火箭图标 -->
      <div class="app-icon-container">
        <img src="@/assets/images/popDialog/update.png" alt="" />
      </div>

      <div class="update-container top-bg" @click.stop>
        <div class="bottom-bg">
          <span class="close-btn">
            <ZIcon type="icon-close" class="close-icon" :size="26"></ZIcon>
            <!-- <ZIcon type="icon-jia" class="close-icon" :size="16"></ZIcon> -->
          </span>
          <!-- 内容区域 -->
          <div class="content-area">
            <!-- 标题 -->
            <div class="title">Found a new version!</div>

            <!-- 版本号 -->
            <div class="version">{{ version }}</div>

            <!-- 更新描述 -->
            <div class="update-content" v-if="updateList.length > 0">
              <div class="description-title">{{ descriptionTitle }}</div>
              <div class="update-list">
                <div v-for="(item, index) in updateList" :key="index" class="update-item">
                  {{ index + 1 }}. {{ item }}
                </div>
              </div>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="button-area">
            <GradientButton text="Update" @click="handleUpdate" />
          </div>
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
// @ts-ignore
import GradientButton from "@/components/GradientButton/index.vue";

// Props定义
interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue?: boolean;
  /** 版本号 */
  version?: string;
  /** 更新描述标题 */
  descriptionTitle?: string;
  /** 更新内容列表 */
  updateList?: string[];
  /** 是否允许点击遮罩关闭 */
  maskClosable?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  version: __APP_VERSION__,
  descriptionTitle: "Update description:",
  updateList: () => [],
  maskClosable: false,
});

// Emits定义
const emit = defineEmits<{
  "update:modelValue": [value: boolean];
  close: [];
  update: [];
}>();

// 内部状态
const showUpdateDialog = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 监听内部状态变化
watch(showUpdateDialog, (newVal) => {
  emit("update:modelValue", newVal);
});

// 关闭弹窗
const handleClose = () => {
  showUpdateDialog.value = false;
  emit("close");
};

// 更新按钮点击
const handleUpdate = () => {
  emit("update");
  // 强制更新逻辑
  // 通常强制更新后不关闭弹窗，由外部控制
  handleClose();
};

// 暴露方法供外部调用
defineExpose({
  show: () => {
    showUpdateDialog.value = true;
  },
  hide: () => {
    showUpdateDialog.value = false;
  },
});
</script>

<style lang="scss" scoped>
.update-wrap {
  position: relative;
}

.app-icon-container {
  position: absolute;
  top: 3%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  z-index: 10;

  img {
    width: 120px;
    height: 120px;
  }
}

.update-container,
.bottom-bg {
  font-family: "Inter";
  position: relative;
  width: 335px;
  background: #fff;
  border-radius: 20px;
  box-sizing: border-box;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.top-bg {
  background: linear-gradient(90deg, #fffde9 0%, #fff3d3 50%, #ffe6e1 100%);
}
.bottom-bg {
  padding: 92px 24px 24px;
  min-height: 240px; /* 设置最小高度确保基本布局 */
  background: linear-gradient(180deg, transparent 0%, #ffffff 45%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.close-btn {
  position: absolute;
  top: 14px;
  right: 14px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  .close-icon {
    background: linear-gradient(90deg, #ffbe55 0%, #ff552a 100%);
    -webkit--background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: 500;
  }
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.button-area {
  flex-shrink: 0; /* 防止按钮区域被压缩 */
  margin-top: auto; /* 确保按钮在底部 */
}

.title {
  color: #ff5100;
  margin-bottom: 8px;
  font-weight: 800;
  font-size: 22px;
  line-height: 100%;
  text-align: center;
}

.version {
  color: #ff5100;
  background: #fff8f6;
  margin: 0 auto 20px;
  width: 86px;
  height: 35px;
  padding: 8px 20px;
  gap: 10px;
  opacity: 1;
  border-radius: 999px;

  /* 文本居中样式 */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
}

.update-content {
  margin-bottom: 20px;
  text-align: left;
  flex: 1; /* 允许内容区域自适应高度 */
}

.description-title {
  color: #222;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}

.update-list {
  .update-item {
    color: #666;
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    padding-left: 4px;
  }
}

/* 响应式设计 */
@media (max-height: 600px) {
  .bottom-bg {
    min-height: 250px;
    padding: 80px 20px 20px;
  }

  .app-icon-container img {
    width: 100px;
    height: 100px;
  }

  .title {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .version {
    margin-bottom: 16px;
  }

  .update-content {
    margin-bottom: 16px;
  }
}

@media (max-width: 375px) {
  .update-container,
  .bottom-bg {
    width: 300px;
  }

  .bottom-bg {
    padding: 80px 16px 16px;
  }
}
</style>
