<template>
  <ZPopOverlay :show="showUpdateDialog" @click="handleMaskClick">
    <div class="update-wrap" role="dialog" aria-modal="true" aria-labelledby="update-title">
      <!-- 更新图标 -->
      <div class="app-icon-container" aria-hidden="true">
        <img src="@/assets/images/popDialog/update.png" alt="Update icon" />
      </div>

      <div class="update-container top-bg" @click.stop>
        <div class="bottom-bg">
          <!-- 关闭按钮 -->
          <button class="close-btn" type="button" aria-label="Close dialog" @click="closeDialog">
            <ZIcon type="icon-close" class="close-icon" :size="26" />
          </button>

          <!-- 内容区域 -->
          <div class="content-area">
            <!-- 标题 -->
            <h2 id="update-title" class="title">Found a new version!</h2>

            <!-- 版本号 -->
            <div class="version" aria-label="Version">{{ version }}</div>

            <!-- 更新描述 -->
            <div v-if="hasUpdateContent" class="update-content">
              <h3 class="description-title">{{ descriptionTitle }}</h3>
              <ul class="update-list" role="list">
                <li
                  v-for="(item, index) in updateList"
                  :key="`update-item-${index}`"
                  class="update-item"
                >
                  {{ item }}
                </li>
              </ul>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="button-area">
            <GradientButton text="Update" @click="handleUpdate" />
          </div>
        </div>
      </div>
    </div>
  </ZPopOverlay>
</template>

<script lang="ts" setup>
import { computed } from "vue";
// @ts-ignore
import GradientButton from "@/components/GradientButton/index.vue";

/**
 * 强制更新弹窗组件 Props
 */
interface Props {
  /** 控制弹窗显示/隐藏 */
  modelValue?: boolean;
  /** 版本号 */
  version?: string;
  /** 更新描述标题 */
  descriptionTitle?: string;
  /** 更新内容列表 */
  updateList?: string[];
  /** 是否允许点击遮罩关闭 */
  maskClosable?: boolean;
}

/**
 * 组件事件定义
 */
interface Emits {
  "update:modelValue": [value: boolean];
  close: [];
  update: [];
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  version: __APP_VERSION__,
  descriptionTitle: "Update description:",
  updateList: () => [],
  maskClosable: false,
});

const emit = defineEmits<Emits>();

/**
 * 弹窗显示状态的双向绑定
 */
const showUpdateDialog = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

/**
 * 是否显示更新内容列表
 */
const hasUpdateContent = computed(() => props.updateList.length > 0);

/**
 * 关闭弹窗的通用方法
 */
const closeDialog = () => {
  showUpdateDialog.value = false;
  emit("close");
};

/**
 * 处理遮罩点击事件
 */
const handleMaskClick = () => {
  if (props.maskClosable) {
    closeDialog();
  } else {
    console.warn("强制更新弹窗不允许通过点击遮罩关闭，请点击更新按钮");
  }
};

/**
 * 处理更新按钮点击事件
 */
const handleUpdate = () => {
  emit("update");
  // 通常强制更新后不关闭弹窗，由外部控制
  closeDialog();
};

/**
 * 暴露给父组件的方法
 */
defineExpose({
  /** 显示弹窗 */
  show: () => (showUpdateDialog.value = true),
  /** 隐藏弹窗 */
  hide: () => (showUpdateDialog.value = false),
  /** 关闭弹窗（触发 close 事件） */
  close: closeDialog,
});
</script>

<style lang="scss" scoped>
.update-wrap {
  position: relative;
}

.app-icon-container {
  position: absolute;
  top: 6%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  justify-content: center;
  z-index: 10;

  img {
    width: 120px;
    height: 120px;
  }
}

.update-container,
.bottom-bg {
  font-family: "Inter";
  position: relative;
  width: 335px;
  background: #fff;
  border-radius: 20px;
  box-sizing: border-box;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}
.top-bg {
  background: linear-gradient(90deg, #fffde9 0%, #fff3d3 50%, #ffe6e1 100%);
}
.bottom-bg {
  padding: 92px 24px 24px;
  min-height: 240px; /* 设置最小高度确保基本布局 */
  background: linear-gradient(180deg, transparent 0%, #ffffff 45%);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.close-btn {
  position: absolute;
  top: 14px;
  right: 14px;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
  }

  &:active {
    transform: scale(0.95);
  }

  .close-icon {
    background: linear-gradient(90deg, #ffbe55 0%, #ff552a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
    font-weight: 500;
  }
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.button-area {
  margin: 0 auto;
  width: 228px;
  flex-shrink: 0; /* 防止按钮区域被压缩 */
  margin-top: auto; /* 确保按钮在底部 */
}

.title {
  color: #ff5100;
  margin-bottom: 8px;
  font-weight: 800;
  font-size: 22px;
  line-height: 100%;
  text-align: center;
}

.version {
  color: #ff5100;
  background: #fff8f6;
  margin: 0 auto 20px;
  width: 86px;
  height: 35px;
  padding: 8px 20px;
  gap: 10px;
  opacity: 1;
  border-radius: 999px;

  /* 文本居中样式 */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;

  font-weight: 500;
  font-size: 16px;
  line-height: 100%;
}

.update-content {
  margin-bottom: 20px;
  text-align: left;
  flex: 1; /* 允许内容区域自适应高度 */
}

.description-title {
  color: #222;
  margin-bottom: 12px;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}

.update-list {
  margin: 0;
  padding: 0;
  list-style: none;

  .update-item {
    color: #666;
    margin-bottom: 8px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    padding-left: 16px;
    position: relative;

    &::before {
      content: "•";
      color: #ff5100;
      font-weight: bold;
      position: absolute;
      left: 0;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* 响应式设计 */
@media (max-height: 600px) {
  .bottom-bg {
    min-height: 250px;
    padding: 80px 20px 20px;
  }

  .app-icon-container img {
    width: 100px;
    height: 100px;
  }

  .title {
    font-size: 20px;
    margin-bottom: 6px;
  }

  .version {
    margin-bottom: 16px;
  }

  .update-content {
    margin-bottom: 16px;
  }
}

@media (max-width: 375px) {
  .update-container,
  .bottom-bg {
    width: 300px;
  }

  .bottom-bg {
    padding: 80px 16px 16px;
  }
}
</style>
